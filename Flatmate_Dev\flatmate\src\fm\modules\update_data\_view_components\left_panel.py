"""
Left panel manager for the Update Data module.
Manages the action and navigation panel with common operations.
"""

from PySide6.QtCore import Signal
from PySide6.QtWidgets import <PERSON><PERSON>abel, QVBoxLayout

# Temporarily comment out to break circular import
# from fm.gui._shared_components import BasePanelComponent
from PySide6.QtWidgets import QWidget
from .left_panel_components.widgets.widgets import LeftPanelButtonsWidget


class LeftPanelManager(QWidget):
    """Manager for the left panel containing navigation and action buttons."""
    
    # Signals for publishing events to subscribers
    publish_welcome_selected = Signal() # should possibly be events, these may be speculative and unimplemented ) 
    publish_file_selected = Signal()
    publish_data_selected = Signal()
    publish_exit_selected = Signal()
    
    def __init__(self, parent=None):
        """Initialize the left panel manager."""
        super().__init__(parent)
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Panel title
        title = QLabel("Actions")
        title.setObjectName("panel_title")
        layout.addWidget(title)
        
        # Create the buttons widget
        self.buttons_widget = LeftPanelButtonsWidget(self)
        layout.addWidget(self.buttons_widget)
    
    def _connect_signals(self):
        """Connect signals between components."""
        # Connect button widget signals to our publish signals
        self.buttons_widget.process_clicked.connect(self._on_process_clicked)
        self.buttons_widget.cancel_clicked.connect(self._on_cancel_clicked)
        self.buttons_widget.source_select_requested.connect(self._on_source_select)
    
    def _on_process_clicked(self):
        """Handle process button click."""
        # This could be file or data display depending on context
        self.publish_data_selected.emit()
    
    def _on_cancel_clicked(self):
        """Handle cancel/exit button click."""
        self.publish_exit_selected.emit()
    
    def _on_source_select(self, source_type):
        """Handle source selection."""
        self.publish_file_selected.emit()
    
    def show_component(self):
        """Show the left panel."""
        self.setVisible(True)
    
    def hide_component(self):
        """Hide the left panel."""
        self.setVisible(False)
    
    def set_process_mode(self):
        """Set the panel to process mode."""
        self.buttons_widget.set_process_mode()
    
    def set_exit_mode(self):
        """Set the panel to exit mode."""
        self.buttons_widget.set_exit_mode()
