"""
Update Data view implementation.
"""

import os
import sys
from pathlib import Path
from typing import List, Optional

import pandas as pd
from PySide6.QtCore import Signal
from PySide6.QtWidgets import QFileDialog, QMessageBox

from ...core.services.event_bus import Events, global_event_bus
from ..base.base_module_view import BaseModuleView
# Import view components normally - no circular imports exist
from ._view_components.center_panel import CenterPanelManager
from ._view_components.left_panel import LeftPanelManager
from ._view_components.right_panel import RightPanelManager


class UpdateDataView(BaseModuleView):
    """Update Data view."""

    # ------------------
    # Signals
    # ------------------
    source_select_requested = Signal(str)
    save_select_requested = Signal()
    process_clicked = Signal()
    cancel_clicked = Signal()
    source_option_changed = Signal(str)
    save_option_changed = Signal(str)
    update_database_changed = Signal(bool)  # Signal for database update checkbox

    def __init__(self, parent=None, gui_config=None, gui_keys=None):
        """Initialize the view.

        Args:
            parent: Parent widget
            gui_config: Injected GUI configuration service
            gui_keys: Injected GUI configuration keys
        """
        # Initialize basic properties first
        self.event_bus = global_event_bus
        # Then call parent constructor which will call setup_ui
        super().__init__(parent, gui_config, gui_keys)

    def setup_ui(self):
        """Initial UI setup - called by base class."""
        # Create widgets - simple working approach
        from ._view_components.left_panel_components.widgets import LeftPanelButtonsWidget

        self.left_buttons = LeftPanelButtonsWidget()
        self.center_display = CenterPanelManager()

        # Create guide pane for contextual feedback
        self._create_guide_pane()

        self._connect_signals()

    def _connect_signals(self):
        """Connect internal signals to be forwarded."""
        # Forward all signals from left panel manager (not directly from widgets)
        self.left_panel.buttons_widget.source_select_requested.connect(
            self.source_select_requested.emit
        )
        self.left_panel.buttons_widget.save_select_requested.connect(self.save_select_requested.emit)
        self.left_panel.buttons_widget.process_clicked.connect(self.process_clicked.emit)
        self.left_panel.buttons_widget.cancel_clicked.connect(self.cancel_clicked.emit)
        self.left_panel.buttons_widget.source_option_changed.connect(self.source_option_changed.emit)
        self.left_panel.buttons_widget.save_option_changed.connect(self.save_option_changed.emit)
        self.left_panel.buttons_widget.update_database_changed.connect(
            self.update_database_changed.emit
        )

    def setup_left_panel(self, layout):
        """Set up the left panel with control buttons."""
        layout.addWidget(self.left_panel)
        layout.addWidget(self.guide_pane)

    def setup_center_panel(self, layout):
        """Set up the center panel with display areas."""
        layout.addWidget(self.center_display)

    def disconnect_signals(self):
        """Clean up signal connections."""
        if hasattr(self, "left_panel"):
            self.left_panel.buttons_widget.source_select_requested.disconnect()
            self.left_panel.buttons_widget.save_select_requested.disconnect()
            self.left_panel.buttons_widget.process_clicked.disconnect()
            self.left_panel.buttons_widget.cancel_clicked.disconnect()
            self.left_panel.buttons_widget.source_option_changed.disconnect()
            self.left_panel.buttons_widget.save_option_changed.disconnect()
            self.left_panel.buttons_widget.update_database_changed.disconnect()

    # ------------------
    # UI State Methods
    # ------------------
    def set_exit_mode(self):
        """Set left panel to exit mode."""
        self.left_panel.set_exit_mode()

    def set_process_mode(self):
        """Set left panel to process mode."""
        self.left_panel.set_process_mode()

    def set_save_select_enabled(self, enabled: bool):
        """Enable/disable save select button."""
        self.left_panel.buttons_widget.set_save_select_enabled(enabled)

    def get_update_database(self) -> bool:
        """Get the current state of the update database checkbox."""
        return self.left_panel.buttons_widget.get_update_database()

    def set_source_option(self, option: str):
        """Set the source option in the left panel."""
        self.left_panel.buttons_widget.set_source_option(option)

    def set_update_database(self, checked: bool):
        """Set the state of the update database checkbox."""
        self.left_panel.buttons_widget.set_update_database(checked)

    # ------------------
    # SimpleStateCoordinator Interface Methods
    # ------------------
    def set_process_enabled(self, enabled: bool):
        """Enable/disable the process button."""
        print(f"[STATE_COORDINATOR] Setting process button enabled: {enabled}")  # Debug logging
        if hasattr(self.left_panel.buttons_widget, 'process_btn'):
            self.left_panel.buttons_widget.process_btn.setEnabled(enabled)

    def set_archive_enabled(self, enabled: bool):
        """Enable/disable the archive section."""
        print(f"[STATE_COORDINATOR] Setting archive section enabled: {enabled}")  # Debug logging
        self.set_save_select_enabled(enabled)

    def set_process_text(self, text: str):
        """Set the process button text."""
        print(f"[STATE_COORDINATOR] Setting process button text: {text}")  # Debug logging
        if hasattr(self.left_panel.buttons_widget, 'process_btn'):
            self.left_panel.buttons_widget.process_btn.setText(text)

    def set_all_controls_enabled(self, enabled: bool):
        """Enable/disable all controls during processing."""
        if hasattr(self.left_panel.buttons_widget, 'process_btn'):
            self.left_panel.buttons_widget.process_btn.setEnabled(enabled)
        if hasattr(self.left_panel.buttons_widget, 'source_select_btn'):
            self.left_panel.buttons_widget.source_select_btn.setEnabled(enabled)
        if hasattr(self.left_panel.buttons_widget, 'save_select_btn'):
            self.left_panel.buttons_widget.save_select_btn.setEnabled(enabled)

    # ------------------
    # Display Methods - interface for presenter
    # ------------------

    def set_save_path(self, path: str):
        """Set the save location path in the center panel."""
        self.center_display.set_save_path(path)

    def display_selected_source(self, source_info: dict):
        """Display the selected source files in the center panel."""
        if not source_info:
            return

        if source_info["type"] == "folder":
            self.center_display.set_source_path(source_info["path"])
            # Pass the full file paths to the file browser
            self.center_display.set_files(
                source_info["file_paths"], source_info["path"]
            )
        else:  # files
            files = source_info["file_paths"]
            source_dir = os.path.dirname(files[0])
            self.center_display.set_source_path(source_dir)
            # Pass the full file paths to the file browser
            self.center_display.set_files(files, source_dir)

    def display_master_csv(self, df: pd.DataFrame):
        """Display a DataFrame in the center panel table."""
        self.center_display.display_master_csv(df)

    def display_welcome(self):
        """Display welcome message in center panel."""
        self.center_display.display_welcome()

    # ------------------
    # Info Methods
    # ------------------
    # Status bar-related methods removed as they're redundant
    # The presenter should use InfoBarService directly or publish events
    # through the event bus. The UpdateDataStatusBar now subscribes to
    # these events directly.

    # ------------------
    # Dialog Methods
    # ------------------
    def show_error(self, message: str, title: str = "Error"):
        """Show error message box."""
        QMessageBox.critical(self, title, message)

    def show_success(self, message: str, title: str = "Success"):
        """Show success message box."""
        QMessageBox.information(self, title, message)

    def get_save_option(self) -> str:
        """Get the current save option from the left panel buttons."""
        return self.left_panel.buttons_widget.get_save_option()

    def show_folder_dialog(self, title: str, initial_dir: Optional[str] = None) -> str:
        """Show a folder selection dialog and return the selected folder path."""
        # Use proper folder dialog on all platforms
        return QFileDialog.getExistingDirectory(
            self,
            title,
            initial_dir or str(Path.home()),
            QFileDialog.Option.ShowDirsOnly
        )

    def show_files_dialog(
        self,
        title: str,
        initial_dir: Optional[str] = None,
        filter_str: str = "Data Files (*.csv *.CSV)",
    ) -> List[str]:
        """Show a file selection dialog and return the selected file paths."""
        files, _ = QFileDialog.getOpenFileNames(
            self, title, initial_dir or "", filter_str
        )
        return files

    def _create_guide_pane(self):
        """Create guide pane with lazy import to avoid circular dependencies."""
        from ._view_components.center_panel_components.guide_pane import GuidePaneWidget
        self.guide_pane = GuidePaneWidget()


